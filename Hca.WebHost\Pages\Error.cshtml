﻿@page
@model ErrorModel
@{
    ViewData["Title"] = "Error";
}

<div class="row">
    <div class="col-xl-3 col-lg-1"></div>
    <div class="col-xl-6 col-lg-10">
        <div class="card card-default">
            <div class="card-header">
                <small class="text-muted">NEED HELP?</small>
            </div>
            <div class="card-body">
                <p class="text-danger">An error occurred while processing your request.</p>

                @if (User.Identity.IsAuthenticated)
                {
                    @if (Model.IsAdminUser)
                    {
                        <div>
                            <p>As an admin user, you can't send messages to support through this form.</p>
                            <p>Please contact support directly at <a href="mailto:<EMAIL>"><EMAIL></a></p>
                            @if (Model.ShowRequestId)
                            {
                                <p><strong>Request ID:</strong> <code>@Model.RequestId</code></p>
                            }
                        </div>
                    }
                    else if (Model.SendEmailSuccess)
                    {
                        <div>
                            <p class="text-success">Thank you for your message. We will be in touch soon to help resolve this issue.</p>
                            @if (Model.ShowRequestId)
                            {
                                <p><strong>Request ID:</strong> <code>@Model.RequestId</code></p>
                            }
                        </div>
                    }
                    else
                    {
                        <p>We apologize for the inconvenience. Please describe what you were trying to do when this error occurred, and we'll help you resolve it.</p>
                        <form method="post">
                            <div class="form-group">
                                <label for="message">Please describe what happened:</label>
                                <textarea asp-for="Message" class="form-control" placeholder="Describe what you were trying to do when the error occurred..." required></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">Send Support Request</button>
                            @Html.AntiForgeryToken()
                        </form>
                        @if (Model.ShowRequestId)
                        {
                            <p class="mt-3"><small class="text-muted"><strong>Request ID:</strong> <code>@Model.RequestId</code></small></p>
                        }
                    }
                }
                else
                {
                    <div>
                        <p>Please <a href="/Identity/Account/Login">sign in</a> to send a support request, or contact us directly at <a href="mailto:<EMAIL>"><EMAIL></a></p>
                        @if (Model.ShowRequestId)
                        {
                            <p><strong>Request ID:</strong> <code>@Model.RequestId</code></p>
                        }
                    </div>
                }
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-lg-1"></div>
</div>
