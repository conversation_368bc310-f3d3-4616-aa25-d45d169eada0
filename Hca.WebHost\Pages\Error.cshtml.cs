﻿using System;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients.Queries;
using Hca.Lib.Services;
using Hca.WebHost.Pipeline;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Logging;

namespace Hca.WebHost.Pages
{
    [AllowAnonymous]
    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public class ErrorModel : PageModel
    {
        public string RequestId { get; set; }

        public bool ShowRequestId => !string.IsNullOrEmpty(RequestId);

        [BindProperty] public string Message { get; set; }

        public bool SendEmailSuccess { get; private set; }

        public bool IsAdminUser { get; private set; }

        private readonly ILogger<ErrorModel> _logger;
        private readonly ViewManager _viewManager;
        private readonly IMediator _mediator;

        public ErrorModel(ILogger<ErrorModel> logger, ViewManager viewManager, IMediator mediator)
        {
            _logger = logger;
            _viewManager = viewManager;
            _mediator = mediator;
        }

        public async Task OnGetAsync()
        {
            RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier;

            // Log the error details
            var exceptionHandlerPathFeature = HttpContext.Features.Get<IExceptionHandlerPathFeature>();
            if (exceptionHandlerPathFeature?.Error != null)
            {
                _logger.LogError(exceptionHandlerPathFeature.Error,
                    "Unhandled exception occurred. Request ID: {RequestId}, Path: {Path}",
                    RequestId,
                    exceptionHandlerPathFeature.Path);
            }

            // Check if user is admin (only if authenticated)
            if (User.Identity.IsAuthenticated)
            {
                try
                {
                    IsAdminUser = await _viewManager.IsAdminUser;
                }
                catch
                {
                    // If there's an error getting user info, treat as non-admin
                    IsAdminUser = false;
                }
            }
        }

        public async Task OnPostAsync(CancellationToken cancellationToken)
        {
            RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier;

            // Only allow authenticated client users to send support requests
            if (User.Identity.IsAuthenticated)
            {
                try
                {
                    if (await _viewManager.IsClientUser)
                    {
                        var contact = await _mediator.Send(new GetClientContact(_viewManager.UserClientId, _viewManager.UserId), cancellationToken);
                        var newLine = "<br>";

                        var errorContext = $"Error occurred on page. Request ID: {RequestId}{newLine}";
                        var message = $"Support request from {contact.FirstName} {contact.LastName} ({contact.Email}){newLine}" +
                            $"Position: {contact.Position}{newLine}" +
                            $"Phone: {string.Join(" / ", contact.OfficePhone, contact.MobilePhone)}{newLine}" +
                            $"{newLine}{errorContext}" +
                            $"User message: {Message}";

                        var request = new SendEmail(
                            "<EMAIL>",
                            $"Error Support Request from {contact.FirstName} {contact.LastName}",
                            message,
                            contact.Email);

                        await _mediator.Send(request, cancellationToken);

                        SendEmailSuccess = true;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while sending support email for error page. Request ID: {RequestId}", RequestId);
                    // Don't show the error to the user, just log it
                }
            }
        }
    }
}
